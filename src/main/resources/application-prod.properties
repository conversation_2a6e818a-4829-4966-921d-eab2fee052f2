#需要写到k8s配置文件中
logging.file.path=D:/logs/${spring.application.name}/
#需要写到k8s配置文件中
westcatr.boot.file.upload-folder=D:/upload/${spring.application.name}/

spring.datasource.driverClassName=com.mysql.cj.jdbc.Driver
spring.datasource.url=****************************************************************
spring.datasource.username=root
spring.datasource.password=Yhblsqt@wyy888


download.prefix=http://e-car-back-service:7330
westcatr.boot.cache.enable-redis=true
spring.redis.database=5
spring.redis.host=*************
spring.redis.password=Yhblsqt@wyy888
spring.redis.port=16379
spring.redis.timeout=50000
spring.redis.jedis.pool.max-active=16
spring.redis.jedis.pool.max-idle=16
spring.redis.jedis.pool.max-wait=5000
spring.redis.jedis.pool.min-idle=1
westcatr.boot.security.encryption=true
westcatr.boot.security.no-save-time=604800

etravel.api.url=http://e-travel:16666/e-travel/openapi/jiluo
etravel.partner.id=P0000000001
etravel.partner.context.path=/e-travel
etravel.public.key=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEApIToWOWO9EarE7rqjwNbX8VskJz/WXwcIlfuWc3+fnX4pLkwieeC2YSAXzy5Nelm8buX1gOCpe8YSSGo+77Ou63eHGk9oLO5/WqqLvdqIJYG1SszOFmcH2ThwmTRW5ECFFtXRtH2XTGq6eRZ+YCICIAiEci9HBo/uNteiiwtnTye9vufQe24l4Cdp2y1PHYtLIMwYkvPHW3pNeR3i7gnPCLS+Y6Xq+CzUfD7CM13X8w9Y69GAenFtmDaUgtkmoievaP+soePduMjFM/bIBFM+wMdQC4lEcDN3eiU3slrifGrz/h7YsN9hwEaHbTxrLdLXg8q+F92crw+gwhNjCdGEwIDAQAB
etravel.private.key=MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQCb4z9BqWvA+410MLdo2kPOkkGQWhMXKLC/99l32cWuZn9p2ZnvrCsbWA4Mh9nCBLk8F48EQbjdxO6iSZ3lBfZj5Bf++BgGD2R7Aer0iC2G74mjiJ2vq9BdIHsw1e5RJq3Gx1e2kCwEBNlm6CtH6P6IU0MJZcU0Zf4J0+QtPVBFSRkb6Ch2YeaEOPKNnethEMHyfXXcQrjgyiYsEJjVLLTUjB629wHAlPu2uijoZ84S2sOfWmoEp0YnfbhqM/uIStZiWWuXIMUnkpSVQ2kw408YsxdWhLMRBpDuNLJYx6QiQZ86suJnCquSO1JxrHOCPywEKC1i+vkIMoADVfXbjHINAgMBAAECggEAKSqy+skd/qGvsl5nIRZ6z6OKEu/mmLgTLS7rzB3DeZHx1ec0IroJzDfNxfteQ0FyFW93Qj2E73PhPRRJ0UBo1+pVd+zWk0sfUuinvpnzw+2gIIKkec7awK0iOzxTqjSXsaXVAQ5DwW/yZZ2sSFo8VY3/ZwzsLjE9DCVb3aKSfykqAQjhGWTFwnQZSzqrzs4yKifKo8cZfSphwqo72eJPPVAWCXOxwsQnKtAbhNcJjfRgS7zqXxc562/YdQb2Dfc7HFQYD/GmVQjAFQeu+WxpNlHH/RaJ952qodbtiUgxabusBWbHCUDO5BNAdOiP+tWd7RONGr3833HIBZRCiMoooQKBgQD0a6/QNclqj1/cfho79SKyYFX+qFSvdBGjiXSBZYSOW/QLm4UqyYpy4fHvO8tvb/cbo5OKneXcLP2HB632K00VkQN6/glZ6Pkthm2CQLiejDs0/Z99W0uxQk1BAPMnZOx6z8uNddVMjPxMrmThE+N0U0us1uZdm2yeDc7BUQC/mQKBgQCjRdcDavnL6sMcBE2EeWe1Toby+K2Zp5f4OoF9CeM2M0KxFuSPnii5sE09/oTf5q5BIpZPVIIt5kx7kS9mR5GQx+F6nmu2dwsLpgKeCwXViC3F7y+gRFtjvTeE+72rDbHUqN562lfCfxVU5WOTCBv0POMn3dkAlDZ4Xt7YKpEelQKBgBDrt5MpdhUrcc546GYINWu14Z4slxhCZ8ozhN5EYeXdFcsL7C8nHq+PVsliEVZYoPnH3Dn3bmdMsgx8Cp4FJ2P70wrbtVgFGeL98GzZAC7xtFOZz0XFYTsXRPPgFfU/NmPWKNhVNPwEsp64YAF4+AEglxeTFQWb8ewLNqoJaozZAoGALPAuC9zHwB5vcSimBU49AyQH9JwJe/8qzsGbnkS4atSVCJTad8T6RJGH0QZaoB2n3HmekVsbMXLvnhsJxsbwA79gChXMY75EJgKdOc6i3nDK2G3K1/u4g60yuMFpJMlyJqwMABhUi03bGzC/xVo4B3MyumhPL9+bVmoWP70HkDkCgYBPjNHdTaDjDi4Tt15dHfhnzKGkDLNdhzsPaDAw/ysGoW5T7Sfdy5Kb2mwGNUGP20nVv4ZXZLdUOjWqTh+0SM3ycuKSYe9t8Lzo/8WBn3BNQyC4Ww/oIzUjf4zA3onWhgXV9TM5FHLRMxJ7a+4apZ2Xys/boEA7tFpEDA6JWapgNg==
spring.output.ansi.enabled=always

# Dubbo配置
dubbo.application.qosEnable=false
dubbo.registry.parameters.namespace=etravel
dubbo.registry.group=etravel
dubbo.registry.register-mode=interface

# Nacos注册中心配置
dubbo.registry.address=nacos:18848
dubbo.protocol.name=dubbo
dubbo.protocol.port=-1

caradmin.username=caradmin


# 排除自动配置的类
#spring.autoconfigure.exclude=org.apache.dubbo.spring.boot.autoconfigure.DubboAutoConfiguration

file.storage.type=OBS
file.storage.aspect-enabled=true

# 华为云OBS配置
huaweicloud.obs.end-point=obsv3.cq-region-2.sgic.sgcc.com.cn
huaweicloud.obs.ak=EQKJHDLVQNTNBDIX6CUC
huaweicloud.obs.sk=LJEzNHv0gLfu6Uim89oj0lPZOMItiURg19xKt131
huaweicloud.obs.bucket-name=ecxyygkxt-production-obsv3-01
huaweicloud.obs.domain=https://ecxyygkxt-production-obsv3-01.obsv3.cq-region-2.sgic.sgcc.com.cn

# 阿里云OSS配置
aliyun.oss.endpoint=http://oss-cn-cq-cqdlww-d01-a.ops.cq.sgcc.com.cn/
aliyun.oss.access-key-id=dVo0TD4hGELoILff
aliyun.oss.access-key-secret=wxGe0FdhaBnpLC7SSQjNEuEpJa3ZsH
aliyun.oss.bucket-name=ecxyygk
aliyun.oss.domain=https://ecxyygk.oss-cn-cq-cqdlww-d01-a.ops.cq.sgcc.com.cn


# OpenAPI 安全配置
openapi.security.enabled=true
openapi.security.app-id=etravel
openapi.security.app-secret=3EC294C7-9CD6-BE47-5DA3-07C21F9C92FE
openapi.security.sm4-key=31323334353637383930616263646566
openapi.security.sm4-iv=66656463626130393837363534333231
openapi.security.request-expire-seconds=20
openapi.security.debug-mode=false

#错误信息精简返回
westcatr.boot.web.showExceptionDetails=false

# 用户同步
sync.user.etravel.enable=true

# 接口地址
download.url=https://*************:10443/api

# \u542F\u7528\u6587\u4EF6\u4E0A\u4F20\u529F\u80FD
spring.servlet.multipart.enabled=true
# \u8BBE\u7F6E\u5355\u4E2A\u6587\u4EF6\u7684\u6700\u5927\u5927\u5C0F
spring.servlet.multipart.max-file-size=100MB
# \u8BBE\u7F6E\u5355\u6B21\u8BF7\u6C42\u7684\u6587\u4EF6\u7684\u603B\u5927\u5C0F
spring.servlet.multipart.max-request-size=100MB
# \u6307\u5B9A\u6587\u4EF6\u4E0A\u4F20\u7684\u4E34\u65F6\u76EE\u5F55
spring.servlet.multipart.location=${java.io.tmpdir}
# \u6307\u5B9A\u6587\u4EF6\u5199\u5165\u78C1\u76D8\u7684\u9608\u503C
spring.servlet.multipart.file-size-threshold=0
# \u6307\u5B9A\u662F\u5426\u5EF6\u8FDF\u89E3\u6790\u6587\u4EF6
spring.servlet.multipart.resolve-lazily=false
# \u914D\u7F6ETomcat\u53C2\u6570
server.tomcat.max-http-form-post-size=100MB
server.tomcat.max-swallow-size=100MB