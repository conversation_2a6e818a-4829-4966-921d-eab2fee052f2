server.port=7331
logging.file.path=/Users/<USER>/work/logs/${spring.application.name}/
westcatr.boot.file.upload-folder=/Users/<USER>/work/upload/${spring.application.name}/
spring.datasource.driverClassName=com.mysql.cj.jdbc.Driver
#spring.datasource.url=**************************************************************
spring.datasource.url=***********************************************************
spring.datasource.username=root
spring.datasource.password=Ls13548274447
#spring.datasource.username=e_car_db
#spring.datasource.password=westcatr@123
download.prefix=http://************:7330
westcatr.boot.cache.enable-redis=true
spring.redis.database=3
spring.redis.host=127.0.0.1
spring.redis.password=123456
spring.redis.port=6379
spring.redis.timeout=5000
spring.redis.jedis.pool.max-active=16
spring.redis.jedis.pool.max-idle=16
spring.redis.jedis.pool.max-wait=5000
spring.redis.jedis.pool.min-idle=1
westcatr.boot.security.encryption=true
# 排除自动配置的类
#spring.autoconfigure.exclude=org.apache.dubbo.spring.boot.autoconfigure.DubboAutoConfiguration
# ===========================================================
# 文件存储配置（统一）
# ===========================================================
# 文件存储类型: LOCAL(本地), OSS(阿里云), OBS(华为云)
file.storage.type=LOCAL
# 存储模式: SINGLE(单一存储), HYBRID(混合存储)
file.storage.mode=SINGLE
# 是否启用存储切面(需要开启切面才会同步文件)
file.storage.aspect-enabled=false
# 文件同步相关增强配置
file.storage.chunk-size=4194304
file.storage.sync-interval=60000
file.storage.max-retry-count=3
file.storage.chunk-retention-hours=24
file.storage.storage-first=true
# 华为云OBS配置
huaweicloud.obs.end-point=obs.cn-southwest-2.myhuaweicloud.com
huaweicloud.obs.ak=HPUAMVWS2SVNYZ4K01UT
huaweicloud.obs.sk=wQyQU4IF95iemXwPhnY9ps6282C1vJGhFnRZvgw8
huaweicloud.obs.bucket-name=lsobs
huaweicloud.obs.domain=https://lsobs.obs.cn-southwest-2.myhuaweicloud.com
# 阿里云OSS配置
aliyun.oss.endpoint=https://oss-cn-chengdu.aliyuncs.com
aliyun.oss.access-key-id=LTAI5tMPd4K8YLEEfePAvvEu
aliyun.oss.access-key-secret=******************************
aliyun.oss.bucket-name=lsprojectinfo
aliyun.oss.domain=https://lsprojectinfo.oss-cn-chengdu.aliyuncs.com

# 日志级别配置 - 减少重复日志输出
logging.level.com.westcatr.rd.car.business.openapi.controller.FanFanApiController=WARN
logging.level.com.westcatr.rd.car.business.fanfan.impl=WARN
logging.level.com.westcatr.rd.car.business.fanfan=WARN
# 关闭Scheduling任务的日志输出
logging.level.org.springframework.scheduling=WARN