#!/bin/bash

# 定义颜色
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m' # 无颜色

# 解析命令行参数
ARCH=${1:-x86}  # 默认为x86架构

# 验证架构参数
if [[ "$ARCH" != "x86" && "$ARCH" != "arm" ]]; then
    echo -e "${RED}❌ 错误: 不支持的架构 '$ARCH'${NC}"
    echo -e "${YELLOW}💡 用法: $0 [x86|arm]${NC}"
    echo -e "${YELLOW}   x86: 构建x86_64架构镜像 (默认)${NC}"
    echo -e "${YELLOW}   arm: 构建ARM64架构镜像 (Apple Silicon)${NC}"
    exit 1
fi

# 根据架构设置Docker平台和标签
if [[ "$ARCH" == "arm" ]]; then
    DOCKER_PLATFORM="--platform linux/arm64"
    ARCH_LABEL="arm64"
    IMAGE_TAG="e_car_new_back:latest-arm64"
else
    DOCKER_PLATFORM="--platform linux/amd64"
    ARCH_LABEL="x86_64"
    IMAGE_TAG="e_car_new_back:latest"
fi

# 定义时间戳和输出文件名
TIMESTAMP=$(date +"%Y%m%d%H%M%S")
OUTPUT_TAR="e_car_release_${ARCH}_${TIMESTAMP}.tar"

echo -e "${BLUE}===========================================================${NC}"
echo -e "${BLUE}🚀 开始执行E车项目构建与打包流程 - ${ARCH_LABEL}版本${NC}"
echo -e "${BLUE}🏗️  目标架构: ${ARCH_LABEL}${NC}"
echo -e "${BLUE}🐳 Docker平台: ${DOCKER_PLATFORM}${NC}"
echo -e "${BLUE}🔧 已优化: 移除MySQL客户端，使用Flyway进行数据库迁移${NC}"
echo -e "${BLUE}🔍 已优化: 使用Alpine基础镜像减小体积${NC}"
echo -e "${BLUE}===========================================================${NC}"

# 1. Maven 编译打包
echo -e "\n${YELLOW}📦 步骤1: 执行Maven编译打包...${NC}"
mvn clean package -DskipTests

# 检查Maven构建是否成功
if [ $? -ne 0 ]; then
    echo -e "${RED}❌ Maven构建失败，请检查错误并重试${NC}"
    exit 1
fi
echo -e "${GREEN}✅ Maven构建成功！${NC}"

# 2. Docker 镜像构建
echo -e "\n${YELLOW}🐳 步骤2: 构建Docker镜像 (${ARCH_LABEL})...${NC}"
echo -e "${BLUE}📋 构建命令: docker build ${DOCKER_PLATFORM} -t ${IMAGE_TAG} .${NC}"
docker build ${DOCKER_PLATFORM} -t ${IMAGE_TAG} .

# 检查Docker构建是否成功
if [ $? -ne 0 ]; then
    echo -e "${RED}❌ Docker镜像构建失败，请检查错误并重试${NC}"
    exit 1
fi
echo -e "${GREEN}✅ Docker镜像构建成功！${NC}"

# 3. 导出Docker镜像为tar包
echo -e "\n${YELLOW}📤 步骤3: 导出Docker镜像为tar包...${NC}"
echo -e "${BLUE}📋 导出命令: docker save ${IMAGE_TAG} -o ${OUTPUT_TAR}${NC}"
docker save ${IMAGE_TAG} -o ${OUTPUT_TAR}

# 检查导出是否成功
if [ $? -ne 0 ]; then
    echo -e "${RED}❌ Docker镜像导出失败，请检查错误并重试${NC}"
    exit 1
fi

# 显示导出的tar包信息
TAR_SIZE=$(du -h ${OUTPUT_TAR} | cut -f1)
echo -e "${GREEN}✅ Docker镜像导出成功！${NC}"
echo -e "${BLUE}===========================================================${NC}"
echo -e "${GREEN}🎉 全部任务完成！${NC}"
echo -e "${BLUE}架构: ${NC}${ARCH_LABEL}"
echo -e "${BLUE}镜像标签: ${NC}${IMAGE_TAG}"
echo -e "${BLUE}文件名: ${NC}${OUTPUT_TAR}"
echo -e "${BLUE}文件大小: ${NC}${TAR_SIZE}"
echo -e "${BLUE}构建时间: ${NC}$(date)"
echo -e "${BLUE}===========================================================${NC}"
echo -e "\n${YELLOW}💡 部署提示:${NC}"
echo -e "1. 将 ${OUTPUT_TAR} 复制到目标服务器"
echo -e "2. 在目标服务器上执行: ${BLUE}docker load -i ${OUTPUT_TAR}${NC}"
if [[ "$ARCH" == "arm" ]]; then
    echo -e "3. 运行容器: ${BLUE}docker run -d --name e_car_app -p 8080:8080 -e MYSQL_HOST=<数据库主机> -e MYSQL_PORT=<数据库端口> -e MYSQL_USER=<用户名> -e MYSQL_PASSWORD=<密码> -e MYSQL_DATABASE=<数据库名> ${IMAGE_TAG}${NC}"
    echo -e "${YELLOW}⚠️  注意: ARM64镜像只能在ARM64架构的服务器上运行 (如Apple Silicon Mac、ARM64 Linux服务器)${NC}"
else
    echo -e "3. 运行容器: ${BLUE}docker run -d --name e_car_app -p 8080:8080 -e MYSQL_HOST=<数据库主机> -e MYSQL_PORT=<数据库端口> -e MYSQL_USER=<用户名> -e MYSQL_PASSWORD=<密码> -e MYSQL_DATABASE=<数据库名> ${IMAGE_TAG}${NC}"
    echo -e "${YELLOW}💡 x86_64镜像可以在大多数Linux服务器上运行${NC}"
fi
echo -e "4. 数据库变更将通过Flyway自动执行${NC}"

# 显示使用帮助
echo -e "\n${BLUE}📖 脚本使用说明:${NC}"
echo -e "${YELLOW}构建x86架构 (默认): ${NC}$0"
echo -e "${YELLOW}构建x86架构 (显式): ${NC}$0 x86"
echo -e "${YELLOW}构建ARM架构: ${NC}$0 arm"