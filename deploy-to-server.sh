#!/bin/bash

# E-Car 部署到服务器脚本

set -e

# 配置信息
SERVER_HOST="your-k8s-server"
SERVER_USER="your-username"
REMOTE_PATH="/opt/e-car-deployment"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🚀 E-Car 部署到K8s服务器${NC}"

# 检查必要文件
echo -e "${YELLOW}📋 检查必要文件...${NC}"
required_files=(
    "k8s/e-car-configmap.yaml"
    "k8s/e-car-deployment.yaml"
    "k8s/update-config.sh"
)

for file in "${required_files[@]}"; do
    if [[ ! -f "$file" ]]; then
        echo -e "${RED}❌ 文件不存在: $file${NC}"
        exit 1
    fi
done

echo -e "${GREEN}✅ 所有必要文件检查通过${NC}"

# 1. 传输K8s配置文件
echo -e "\n${YELLOW}📤 传输K8s配置文件到服务器...${NC}"
ssh $SERVER_USER@$SERVER_HOST "mkdir -p $REMOTE_PATH/k8s"

scp -r k8s/ $SERVER_USER@$SERVER_HOST:$REMOTE_PATH/

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ 配置文件传输成功${NC}"
else
    echo -e "${RED}❌ 配置文件传输失败${NC}"
    exit 1
fi

# 2. 在服务器上部署
echo -e "\n${YELLOW}🚢 在服务器上部署应用...${NC}"
ssh $SERVER_USER@$SERVER_HOST << EOF
    cd $REMOTE_PATH
    
    echo "部署ConfigMap..."
    kubectl apply -f k8s/e-car-configmap.yaml
    
    echo "部署应用..."
    kubectl apply -f k8s/e-car-deployment.yaml
    
    echo "等待部署完成..."
    kubectl rollout status deployment e-car-back --timeout=300s
    
    echo "查看部署状态..."
    kubectl get pods -l app=e-car-back
    kubectl get svc e-car-back-service
EOF

if [ $? -eq 0 ]; then
    echo -e "${GREEN}🎉 部署成功！${NC}"
else
    echo -e "${RED}❌ 部署失败${NC}"
    exit 1
fi

# 3. 显示访问信息
echo -e "\n${BLUE}📊 部署信息:${NC}"
echo -e "服务器: $SERVER_HOST"
echo -e "部署路径: $REMOTE_PATH"
echo -e "配置更新: ssh $SERVER_USER@$SERVER_HOST 'cd $REMOTE_PATH && ./k8s/update-config.sh'"

echo -e "\n${YELLOW}🔧 后续配置更新命令:${NC}"
echo -e "1. SSH登录服务器: ${BLUE}ssh $SERVER_USER@$SERVER_HOST${NC}"
echo -e "2. 编辑配置: ${BLUE}kubectl edit configmap e-car-config${NC}"
echo -e "3. 重启应用: ${BLUE}kubectl rollout restart deployment e-car-back${NC}"
