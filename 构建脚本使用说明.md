# 构建脚本使用说明

## 📋 概述

`build_and_package.sh` 脚本现在支持多架构构建，可以构建x86_64和ARM64两种架构的Docker镜像。

## 🚀 使用方法

### 基本语法
```bash
./build_and_package.sh [架构]
```

### 支持的架构
- `x86` - 构建x86_64架构镜像（默认）
- `arm` - 构建ARM64架构镜像（Apple Silicon）

## 💻 使用示例

### 1. 构建x86架构（默认）
```bash
# 方法1: 不指定参数（默认x86）
./build_and_package.sh

# 方法2: 显式指定x86
./build_and_package.sh x86
```

**输出文件：** `e_car_release_x86_20241217193000.tar`
**镜像标签：** `e_car_new_back:latest`

### 2. 构建ARM架构
```bash
./build_and_package.sh arm
```

**输出文件：** `e_car_release_arm_20241217193000.tar`
**镜像标签：** `e_car_new_back:latest-arm64`

## 🏗️ 构建过程

脚本会执行以下步骤：

1. **参数验证** - 检查架构参数是否有效
2. **Maven编译** - 执行 `mvn clean package -DskipTests`
3. **Docker构建** - 根据指定架构构建镜像
4. **镜像导出** - 将镜像保存为tar文件

## 📊 输出信息

构建完成后会显示：
- 目标架构
- 镜像标签
- 输出文件名
- 文件大小
- 构建时间
- 部署提示

## 🎯 架构选择指南

### 选择x86架构的情况
- 部署到传统Linux服务器
- 部署到云服务器（AWS、阿里云、腾讯云等）
- 部署到Intel/AMD处理器的服务器
- 需要最大兼容性

### 选择ARM架构的情况
- 在Apple Silicon Mac上运行
- 部署到ARM64 Linux服务器
- 使用AWS Graviton实例
- 本地开发和测试

## ⚠️ 注意事项

### ARM64镜像限制
- ARM64镜像只能在ARM64架构的机器上运行
- 不能在x86服务器上运行ARM64镜像
- 适用于Apple Silicon Mac、ARM64 Linux服务器

### x86_64镜像兼容性
- x86_64镜像可以在大多数Linux服务器上运行
- 兼容性最好，推荐用于生产环境
- 可以在Intel/AMD处理器上运行

## 🔧 Docker平台参数

脚本会自动设置正确的Docker平台参数：

- **x86架构：** `--platform linux/amd64`
- **ARM架构：** `--platform linux/arm64`

## 📝 错误处理

如果输入不支持的架构参数，脚本会：
1. 显示错误信息
2. 显示正确的用法
3. 退出并返回错误码

## 🚀 部署建议

### 生产环境
```bash
# 推荐使用x86架构（兼容性最好）
./build_and_package.sh x86
```

### 本地开发（Apple Silicon Mac）
```bash
# 使用ARM架构（性能更好）
./build_and_package.sh arm
```

### 混合环境
```bash
# 构建两个版本
./build_and_package.sh x86    # 用于生产服务器
./build_and_package.sh arm    # 用于本地开发
```

## 📦 文件命名规则

输出文件名格式：`e_car_release_{架构}_{时间戳}.tar`

示例：
- `e_car_release_x86_20241217193000.tar`
- `e_car_release_arm_20241217193000.tar`

这样可以清楚地区分不同架构的构建产物。
