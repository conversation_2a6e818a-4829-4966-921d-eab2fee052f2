apiVersion: v1
kind: ConfigMap
metadata:
  name: e-car-config-internal
  namespace: default
data:
  # 应用基础配置
  SERVER_PORT: "8080"
  LOGGING_FILE_PATH: "/app/logs"
  WESTCATR_BOOT_FILE_UPLOAD_FOLDER: "/app/uploads"
  DOWNLOAD_PREFIX: "http://e-car-back-service:7330"
  
  # 数据库配置 - 内网环境
  SPRING_DATASOURCE_DRIVER_CLASS_NAME: "com.mysql.cj.jdbc.Driver"
  SPRING_DATASOURCE_URL: "****************************************************************"
  SPRING_DATASOURCE_USERNAME: "root"
  SPRING_DATASOURCE_PASSWORD: "Yhblsqt@wyy888"
  
  # Redis配置 - 内网环境
  WESTCATR_BOOT_CACHE_ENABLE_REDIS: "true"
  SPRING_REDIS_DATABASE: "5"
  SPRING_REDIS_HOST: "*************"
  SPRING_REDIS_PASSWORD: "Yhblsqt@wyy888"
  SPRING_REDIS_PORT: "16379"
  SPRING_REDIS_TIMEOUT: "50000"
  SPRING_REDIS_JEDIS_POOL_MAX_ACTIVE: "16"
  SPRING_REDIS_JEDIS_POOL_MAX_IDLE: "16"
  SPRING_REDIS_JEDIS_POOL_MAX_WAIT: "5000"
  SPRING_REDIS_JEDIS_POOL_MIN_IDLE: "1"
  
  # 安全配置
  WESTCATR_BOOT_SECURITY_ENCRYPTION: "true"
  WESTCATR_BOOT_SECURITY_NO_SAVE_TIME: "604800"
  WESTCATR_BOOT_WEB_SHOW_EXCEPTION_DETAILS: "false"
  
  # E-Travel对接配置
  ETRAVEL_API_URL: "http://e-travel:16666/e-travel/openapi/jiluo"
  ETRAVEL_PARTNER_ID: "P0000000001"
  ETRAVEL_PARTNER_CONTEXT_PATH: "/e-travel"
  ETRAVEL_PUBLIC_KEY: "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEApIToWOWO9EarE7rqjwNbX8VskJz/WXwcIlfuWc3+fnX4pLkwieeC2YSAXzy5Nelm8buX1gOCpe8YSSGo+77Ou63eHGk9oLO5/WqqLvdqIJYG1SszOFmcH2ThwmTRW5ECFFtXRtH2XTGq6eRZ+YCICIAiEci9HBo/uNteiiwtnTye9vufQe24l4Cdp2y1PHYtLIMwYkvPHW3pNeR3i7gnPCLS+Y6Xq+CzUfD7CM13X8w9Y69GAenFtmDaUgtkmoievaP+soePduMjFM/bIBFM+wMdQC4lEcDN3eiU3slrifGrz/h7YsN9hwEaHbTxrLdLXg8q+F92crw+gwhNjCdGEwIDAQAB"
  ETRAVEL_PRIVATE_KEY: "MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQCb4z9BqWvA+410MLdo2kPOkkGQWhMXKLC/99l32cWuZn9p2ZnvrCsbWA4Mh9nCBLk8F48EQbjdxO6iSZ3lBfZj5Bf++BgGD2R7Aer0iC2G74mjiJ2vq9BdIHsw1e5RJq3Gx1e2kCwEBNlm6CtH6P6IU0MJZcU0Zf4J0+QtPVBFSRkb6Ch2YeaEOPKNnethEMHyfXXcQrjgyiYsEJjVLLTUjB629wHAlPu2uijoZ84S2sOfWmoEp0YnfbhqM/uIStZiWWuXIMUnkpSVQ2kw408YsxdWhLMRBpDuNLJYx6QiQZ86suJnCquSO1JxrHOCPywEKC1i+vkIMoADVfXbjHINAgMBAAECggEAKSqy+skd/qGvsl5nIRZ6z6OKEu/mmLgTLS7rzB3DeZHx1ec0IroJzDfNxfteQ0FyFW93Qj2E73PhPRRJ0UBo1+pVd+zWk0sfUuinvpnzw+2gIIKkec7awK0iOzxTqjSXsaXVAQ5DwW/yZZ2sSFo8VY3/ZwzsLjE9DCVb3aKSfykqAQjhGWTFwnQZSzqrzs4yKifKo8cZfSphwqo72eJPPVAWCXOxwsQnKtAbhNcJjfRgS7zqXxc562/YdQb2Dfc7HFQYD/GmVQjAFQeu+WxpNlHH/RaJ952qodbtiUgxabusBWbHCUDO5BNAdOiP+tWd7RONGr3833HIBZRCiMoooQKBgQD0a6/QNclqj1/cfho79SKyYFX+qFSvdBGjiXSBZYSOW/QLm4UqyYpy4fHvO8tvb/cbo5OKneXcLP2HB632K00VkQN6/glZ6Pkthm2CQLiejDs0/Z99W0uxQk1BAPMnZOx6z8uNddVMjPxMrmThE+N0U0us1uZdm2yeDc7BUQC/mQKBgQCjRdcDavnL6sMcBE2EeWe1Toby+K2Zp5f4OoF9CeM2M0KxFuSPnii5sE09/oTf5q5BIpZPVIIt5kx7kS9mR5GQx+F6nmu2dwsLpgKeCwXViC3F7y+gRFtjvTeE+72rDbHUqN562lfCfxVU5WOTCBv0POMn3dkAlDZ4Xt7YKpEelQKBgBDrt5MpdhUrcc546GYINWu14Z4slxhCZ8ozhN5EYeXdFcsL7C8nHq+PVsliEVZYoPnH3Dn3bmdMsgx8Cp4FJ2P70wrbtVgFGeL98GzZAC7xtFOZz0XFYTsXRPPgFfU/NmPWKNhVNPwEsp64YAF4+AEglxeTFQWb8ewLNqoJaozZAoGALPAuC9zHwB5vcSimBU49AyQH9JwJe/8qzsGbnkS4atSVCJTad8T6RJGH0QZaoB2n3HmekVsbMXLvnhsJxsbwA79gChXMY75EJgKdOc6i3nDK2G3K1/u4g60yuMFpJMlyJqwMABhUi03bGzC/xVo4B3MyumhPL9+bVmoWP70HkDkCgYBPjNHdTaDjDi4Tt15dHfhnzKGkDLNdhzsPaDAw/ysGoW5T7Sfdy5Kb2mwGNUGP20nVv4ZXZLdUOjWqTh+0SM3ycuKSYe9t8Lzo/8WBn3BNQyC4Ww/oIzUjf4zA3onWhgXV9TM5FHLRMxJ7a+4apZ2Xys/boEA7tFpEDA6JWapgNg=="
  
  # Dubbo配置
  DUBBO_APPLICATION_QOS_ENABLE: "false"
  DUBBO_REGISTRY_PARAMETERS_NAMESPACE: "etravel"
  DUBBO_REGISTRY_GROUP: "etravel"
  DUBBO_REGISTRY_REGISTER_MODE: "interface"
  DUBBO_REGISTRY_ADDRESS: "nacos:18848"
  DUBBO_PROTOCOL_NAME: "dubbo"
  DUBBO_PROTOCOL_PORT: "-1"
  
  # 文件存储配置
  FILE_STORAGE_TYPE: "OBS"
  FILE_STORAGE_ASPECT_ENABLED: "true"
  
  # 华为云OBS配置
  HUAWEICLOUD_OBS_END_POINT: "obsv3.cq-region-2.sgic.sgcc.com.cn"
  HUAWEICLOUD_OBS_AK: "EQKJHDLVQNTNBDIX6CUC"
  HUAWEICLOUD_OBS_SK: "LJEzNHv0gLfu6Uim89oj0lPZOMItiURg19xKt131"
  HUAWEICLOUD_OBS_BUCKET_NAME: "ecxyygkxt-production-obsv3-01"
  HUAWEICLOUD_OBS_DOMAIN: "https://ecxyygkxt-production-obsv3-01.obsv3.cq-region-2.sgic.sgcc.com.cn"
  
  # 阿里云OSS配置
  ALIYUN_OSS_ENDPOINT: "http://oss-cn-cq-cqdlww-d01-a.ops.cq.sgcc.com.cn/"
  ALIYUN_OSS_ACCESS_KEY_ID: "dVo0TD4hGELoILff"
  ALIYUN_OSS_ACCESS_KEY_SECRET: "wxGe0FdhaBnpLC7SSQjNEuEpJa3ZsH"
  ALIYUN_OSS_BUCKET_NAME: "ecxyygk"
  ALIYUN_OSS_DOMAIN: "https://ecxyygk.oss-cn-cq-cqdlww-d01-a.ops.cq.sgcc.com.cn"
  
  # OpenAPI安全配置
  OPENAPI_SECURITY_ENABLED: "true"
  OPENAPI_SECURITY_APP_ID: "etravel"
  OPENAPI_SECURITY_APP_SECRET: "3EC294C7-9CD6-BE47-5DA3-07C21F9C92FE"
  OPENAPI_SECURITY_SM4_KEY: "31323334353637383930616263646566"
  OPENAPI_SECURITY_SM4_IV: "66656463626130393837363534333231"
  OPENAPI_SECURITY_REQUEST_EXPIRE_SECONDS: "20"
  OPENAPI_SECURITY_DEBUG_MODE: "false"
  
  # 用户同步配置
  SYNC_USER_ETRAVEL_ENABLE: "true"
  
  # 接口地址配置
  DOWNLOAD_URL: "https://*************:10443/api"
  
  # 文件上传配置
  SPRING_SERVLET_MULTIPART_ENABLED: "true"
  SPRING_SERVLET_MULTIPART_MAX_FILE_SIZE: "100MB"
  SPRING_SERVLET_MULTIPART_MAX_REQUEST_SIZE: "100MB"
  SPRING_SERVLET_MULTIPART_LOCATION: "${java.io.tmpdir}"
  SPRING_SERVLET_MULTIPART_FILE_SIZE_THRESHOLD: "0"
  SPRING_SERVLET_MULTIPART_RESOLVE_LAZILY: "false"
  
  # Tomcat配置
  SERVER_TOMCAT_MAX_HTTP_FORM_POST_SIZE: "100MB"
  SERVER_TOMCAT_MAX_SWALLOW_SIZE: "100MB"
  SERVER_TOMCAT_CONNECTION_TIMEOUT: "1800000"
  SERVER_TOMCAT_KEEP_ALIVE_TIMEOUT: "1800000"
  
  # 输出配置
  SPRING_OUTPUT_ANSI_ENABLED: "always"
  
  # 管理员配置
  CARADMIN_USERNAME: "cargws"

