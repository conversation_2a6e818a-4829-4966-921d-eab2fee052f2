apiVersion: apps/v1
kind: Deployment
metadata:
  name: e-car-back-mac
  namespace: default
  labels:
    app: e-car-back-mac
spec:
  replicas: 1
  selector:
    matchLabels:
      app: e-car-back-mac
  template:
    metadata:
      labels:
        app: e-car-back-mac
    spec:
      containers:
      - name: e-car-back
        image: e_car_new_back:latest
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 7331
        env:
        # 使用 ConfigMap 中的外部配置文件
        - name: SPRING_CONFIG_IMPORT
          value: "optional:configtree:/etc/config/"
        # 你也可以在这里继续添加其他环境变量
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 7331
          initialDelaySeconds: 120
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 7331
          initialDelaySeconds: 60
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        resources:
          requests:
            cpu: "200m"
            memory: "512Mi"
          limits:
            cpu: "1000m"
            memory: "2Gi"
        volumeMounts:
        - name: logs-volume
          mountPath: /app/logs
        - name: uploads-volume
          mountPath: /app/uploads
        - name: config-volume
          mountPath: /etc/config/application-mac.properties
          subPath: application-mac.properties
      volumes:
      - name: logs-volume
        emptyDir: {}
      - name: uploads-volume
        emptyDir: {}
      - name: config-volume
        configMap:
          name: e-car-config-mac
      restartPolicy: Always
---
apiVersion: v1
kind: Service
metadata:
  name: e-car-back-service-mac
  namespace: default
spec:
  selector:
    app: e-car-back-mac
  ports:
    - protocol: TCP
      port: 7331
      targetPort: 7331
      nodePort: 30731
  type: NodePort