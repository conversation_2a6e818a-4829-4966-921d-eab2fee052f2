apiVersion: v1
kind: ConfigMap
metadata:
  name: e-car-config-mac
  namespace: default
data:
  # 应用基础配置 - 使用环境变量格式
  SERVER_PORT: "7331"
  LOGGING_FILE_PATH: "/app/logs"
  WESTCATR_BOOT_FILE_UPLOAD_FOLDER: "/app/uploads"
  DOWNLOAD_PREFIX: "http://************:7330"
  
  # 数据库配置 - 使用环境变量格式（大写+下划线）
  SPRING_DATASOURCE_DRIVER_CLASS_NAME: "com.mysql.cj.jdbc.Driver"
  SPRING_DATASOURCE_URL: "*****************************************************************"
  SPRING_DATASOURCE_USERNAME: "root"
  SPRING_DATASOURCE_PASSWORD: "Ls13548274447"
  
  # Redis配置 - 使用环境变量格式
  WESTCATR_BOOT_CACHE_ENABLE_REDIS: "true"
  SPRING_REDIS_DATABASE: "3"
  SPRING_REDIS_HOST: "***************"
  SPRING_REDIS_PASSWORD: "123456"
  SPRING_REDIS_PORT: "6379"
  SPRING_REDIS_TIMEOUT: "5000"
  SPRING_REDIS_JEDIS_POOL_MAX_ACTIVE: "16"
  SPRING_REDIS_JEDIS_POOL_MAX_IDLE: "16"
  SPRING_REDIS_JEDIS_POOL_MAX_WAIT: "5000"
  SPRING_REDIS_JEDIS_POOL_MIN_IDLE: "1"
  
  # 安全配置
  westcatr.boot.security.encryption: "true"
  
  # 自动配置排除
  spring.autoconfigure.exclude: "org.apache.dubbo.spring.boot.autoconfigure.DubboAutoConfiguration"
  
  # 文件存储配置
  file.storage.type: "LOCAL"
  file.storage.mode: "SINGLE"
  file.storage.aspect-enabled: "false"
  file.storage.chunk-size: "4194304"
  file.storage.sync-interval: "60000"
  file.storage.max-retry-count: "3"
  file.storage.chunk-retention-hours: "24"
  file.storage.storage-first: "true"
  
  # 华为云OBS配置
  huaweicloud.obs.end-point: "obs.cn-southwest-2.myhuaweicloud.com"
  huaweicloud.obs.ak: "HPUAMVWS2SVNYZ4K01UT"
  huaweicloud.obs.sk: "wQyQU4IF95iemXwPhnY9ps6282C1vJGhFnRZvgw8"
  huaweicloud.obs.bucket-name: "lsobs"
  huaweicloud.obs.domain: "https://lsobs.obs.cn-southwest-2.myhuaweicloud.com"
  
  # 阿里云OSS配置
  aliyun.oss.endpoint: "https://oss-cn-chengdu.aliyuncs.com"
  aliyun.oss.access-key-id: "LTAI5tMPd4K8YLEEfePAvvEu"
  aliyun.oss.access-key-secret: "******************************"
  aliyun.oss.bucket-name: "lsprojectinfo"
  aliyun.oss.domain: "https://lsprojectinfo.oss-cn-chengdu.aliyuncs.com"
  
  # 日志级别配置 - 减少重复日志输出
  logging.level.com.westcatr.rd.car.business.openapi.controller.FanFanApiController: "WARN"
  logging.level.com.westcatr.rd.car.business.fanfan.impl: "WARN"
  logging.level.com.westcatr.rd.car.business.fanfan: "WARN"
  logging.level.org.springframework.scheduling: "WARN"
