#!/bin/bash

# E-Car Mac环境K8s配置测试脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

CONFIGMAP_NAME="e-car-config-mac"
DEPLOYMENT_NAME="e-car-back-mac"
SERVICE_NAME="e-car-back-service-mac"

echo -e "${BLUE}🚀 E-Car Mac环境K8s配置测试${NC}"
echo ""

# 检查kubectl是否可用
if ! command -v kubectl &> /dev/null; then
    echo -e "${RED}❌ kubectl 未安装或不在PATH中${NC}"
    exit 1
fi

if ! kubectl cluster-info &> /dev/null; then
    echo -e "${RED}❌ 无法连接到Kubernetes集群${NC}"
    exit 1
fi

echo -e "${GREEN}✅ kubectl 连接正常${NC}"

# 1. 部署ConfigMap
echo -e "\n${YELLOW}📝 步骤1: 部署ConfigMap...${NC}"
kubectl apply -f k8s/e-car-configmap-mac.yaml

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ ConfigMap部署成功${NC}"
else
    echo -e "${RED}❌ ConfigMap部署失败${NC}"
    exit 1
fi

# 2. 部署应用
echo -e "\n${YELLOW}🚢 步骤2: 部署应用...${NC}"
kubectl apply -f k8s/e-car-deployment-mac.yaml

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ 应用部署成功${NC}"
else
    echo -e "${RED}❌ 应用部署失败${NC}"
    exit 1
fi

# 3. 等待Pod启动
echo -e "\n${YELLOW}⏳ 步骤3: 等待Pod启动...${NC}"
kubectl rollout status deployment "$DEPLOYMENT_NAME" --timeout=300s

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Pod启动成功${NC}"
else
    echo -e "${RED}❌ Pod启动超时或失败${NC}"
    echo -e "${YELLOW}💡 查看Pod状态: kubectl get pods -l app=$DEPLOYMENT_NAME${NC}"
    echo -e "${YELLOW}💡 查看日志: kubectl logs -l app=$DEPLOYMENT_NAME --tail=50${NC}"
    exit 1
fi

# 4. 显示部署信息
echo -e "\n${BLUE}📊 部署信息:${NC}"
echo -e "${YELLOW}ConfigMap:${NC}"
kubectl get configmap "$CONFIGMAP_NAME"

echo -e "\n${YELLOW}Deployment:${NC}"
kubectl get deployment "$DEPLOYMENT_NAME"

echo -e "\n${YELLOW}Service:${NC}"
kubectl get service "$SERVICE_NAME"

echo -e "\n${YELLOW}Pods:${NC}"
kubectl get pods -l app="$DEPLOYMENT_NAME"

# 5. 获取访问地址
NODE_PORT=$(kubectl get service "$SERVICE_NAME" -o jsonpath='{.spec.ports[0].nodePort}')
echo -e "\n${GREEN}🎉 部署完成！${NC}"
echo -e "${BLUE}访问地址: http://localhost:$NODE_PORT${NC}"
echo -e "${BLUE}健康检查: http://localhost:$NODE_PORT/actuator/health${NC}"

# 6. 显示配置测试命令
echo -e "\n${YELLOW}🔧 配置测试命令:${NC}"
echo -e "查看ConfigMap: ${BLUE}kubectl get configmap $CONFIGMAP_NAME -o yaml${NC}"
echo -e "编辑配置: ${BLUE}kubectl edit configmap $CONFIGMAP_NAME${NC}"
echo -e "重启应用: ${BLUE}kubectl rollout restart deployment $DEPLOYMENT_NAME${NC}"
echo -e "查看日志: ${BLUE}kubectl logs -l app=$DEPLOYMENT_NAME --tail=100${NC}"
echo -e "删除部署: ${BLUE}kubectl delete -f k8s/e-car-deployment-mac.yaml${NC}"

# 7. 测试配置更新
echo -e "\n${YELLOW}🧪 配置更新测试示例:${NC}"
echo -e "更新Redis主机: ${BLUE}kubectl patch configmap $CONFIGMAP_NAME -p='{\"data\":{\"spring.redis.host\":\"新主机地址\"}}'${NC}"
echo -e "更新数据库端口: ${BLUE}kubectl patch configmap $CONFIGMAP_NAME -p='{\"data\":{\"spring.datasource.url\":\"***********************************************************\"}}'${NC}"
echo -e "更新文件存储类型: ${BLUE}kubectl patch configmap $CONFIGMAP_NAME -p='{\"data\":{\"file.storage.type\":\"OBS\"}}'${NC}"

echo -e "\n${GREEN}✨ 测试环境准备完成！您可以开始测试配置更新功能了${NC}"
